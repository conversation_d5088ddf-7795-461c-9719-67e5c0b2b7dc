package com.lfb.android.footprint.service

import android.app.*
import android.content.Intent
import android.os.IBinder
import android.app.NotificationManager
import android.app.NotificationChannel
import android.os.Build
import androidx.core.app.NotificationCompat
import com.lfb.android.footprint.MainMapActivity
import com.lfb.android.footprint.R
import com.lfb.android.footprint.location.LocationManager

class LocationService : Service() {
    private val locationManager by lazy { LocationManager.getInstance(this) }
    private val NOTIFICATION_ID = 1
    private val CHANNEL_ID = "location_service_channel"

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        acquireWakeLock()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 创建通知
        val notification = createNotification()
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, notification)
        
        // 开始位置更新
        locationManager.startLocationUpdates()
        
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Location Service"
            val descriptionText = "用于后台记录位置信息"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val notificationIntent = Intent(this, MainMapActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
//            .setContentTitle("足迹记录中")
            .setContentText("正在后台记录您的轨迹")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
//        locationManager.stopLocationUpdates()
        restartService()
    }

    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "SportsApp:TrackingWakeLock"
        )
        wakeLock.acquire(10*60*1000L) // 10分钟超时
    }

    private fun restartService() {
        val intent = Intent(this, LocationService::class.java)
        intent.action = "START_TRACKING"
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }
} 